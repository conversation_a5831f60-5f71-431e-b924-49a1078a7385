name: Vulnerability Scan

on:
  schedule:
    - cron: '0 0 * * *'    # daily at midnight UTC
  workflow_dispatch:      # manual trigger

jobs:
  scan:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Set up Trivy
        uses: aquasecurity/trivy-action@v0.9.4
        with:
          version: 'latest'

      - name: Run vulnerability scan
        run: |
          ./scan.sh nginx:latest

      - name: Upload report as artifact
        uses: actions/upload-artifact@v3
        with:
          name: trivy-report
          path: trivy-report.json

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          pip install -r requirements.txt

      - name: Parse report & notify Slack
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        run: |
          python parse-report.py
