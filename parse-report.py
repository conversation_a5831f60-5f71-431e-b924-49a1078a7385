#!/usr/bin/env python3
import json
import os
import requests
import sys

# Slack webhook URL from GitHub Secrets
SLACK_WEBHOOK = os.environ["SLACK_WEBHOOK_URL"]
REPORT_FILE   = "trivy-report.json"
MIN_CVSS      = 7.0

def load_report(path):
    with open(path) as f:
        return json.load(f)

def filter_high_vulns(report, threshold):
    high = []
    for result in report.get("Results", []):
        for vuln in result.get("Vulnerabilities", []):
            if vuln.get("SeverityScore", 0) >= threshold:
                high.append({
                    "VulnerabilityID": vuln["VulnerabilityID"],
                    "PkgName": vuln["PkgName"],
                    "InstalledVersion": vuln["InstalledVersion"],
                    "FixedVersion": vuln.get("FixedVersion", "n/a"),
                    "Severity": vuln["Severity"],
                    "Score": vuln["SeverityScore"]
                })
    return high

def post_to_slack(vulns):
    if not vuln_list:
        payload = {"text": ":white_check_mark: No high-severity vulnerabilities found."}
    else:
        text = ":warning: *High-Severity Vulnerabilities Detected* :warning:\n"
        for v in vulns:
            text += f"> `{v['VulnerabilityID']}` {v['PkgName']} ({v['InstalledVersion']} → {v['FixedVersion']}) | Severity: {v['Severity']} ({v['Score']})\n"
        payload = {"text": text}

    resp = requests.post(SLACK_WEBHOOK, json=payload)
    resp.raise_for_status()

if __name__ == "__main__":
    report = load_report(REPORT_FILE)
    vuln_list = filter_high_vulns(report, MIN_CVSS)
    post_to_slack(vuln_list)
    print("Slack notification sent.")
